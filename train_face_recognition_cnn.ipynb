
import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout
from tensorflow.keras.utils import to_categorical
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder



data_dir = "train"
categories = os.listdir(data_dir)
img_size = 100

data = []
labels = []

for category in categories:
    path = os.path.join(data_dir, category)
    for img in os.listdir(path):
        try:
            img_array = cv2.imread(os.path.join(path, img), cv2.IMREAD_GRAYSCALE)
            faces = cv2.CascadeClassifier(cv2.data.haarcascades + "haarcascade_frontalface_default.xml").detectMultiScale(img_array, 1.1, 4)
            for (x, y, w, h) in faces:
                face = img_array[y:y+h, x:x+w]
                face = cv2.resize(face, (img_size, img_size))
                data.append(face)
                labels.append(category)
        except Exception as e:
            pass

X = np.array(data).reshape(-1, img_size, img_size, 1) / 255.0
le = LabelEncoder()
y = le.fit_transform(labels)
y = to_categorical(y)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)



model = Sequential([
    Conv2D(32, (3,3), activation='relu', input_shape=(img_size, img_size, 1)),
    MaxPooling2D(2, 2),
    Conv2D(64, (3,3), activation='relu'),
    MaxPooling2D(2, 2),
    Flatten(),
    Dense(128, activation='relu'),
    Dropout(0.5),
    Dense(len(categories), activation='softmax')
])

model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
model.summary()



history = model.fit(X_train, y_train, epochs=10, validation_data=(X_test, y_test))



model.save("model/face_recognition_model.h5")
import pickle
with open("model/label_encoder.pkl", "wb") as f:
    pickle.dump(le, f)
print("Model and label encoder saved.")
