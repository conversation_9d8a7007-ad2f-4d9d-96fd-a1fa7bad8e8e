!pip install opencv-python tensorflow scikit-learn matplotlib pillow pyttsx3

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, <PERSON>ten, Dense, Dropout, BatchNormalization
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import pickle
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

print("All libraries imported successfully!")

# Configuration
DATASET_DIR = "dataset"  # Change this to your dataset directory
MODEL_DIR = "model"
IMG_SIZE = 128  # Increased size for better accuracy
BATCH_SIZE = 32
EPOCHS = 50
CONFIDENCE_THRESHOLD = 0.7  # Minimum confidence for recognition

# Create model directory if it doesn't exist
os.makedirs(MODEL_DIR, exist_ok=True)

print(f"Dataset directory: {DATASET_DIR}")
print(f"Model directory: {MODEL_DIR}")
print(f"Image size: {IMG_SIZE}x{IMG_SIZE}")

def load_and_preprocess_data(dataset_dir, img_size):
    """
    Load images from dataset directory and preprocess them
    """
    # Initialize Haar Cascade for face detection
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    data = []
    labels = []
    
    # Check if dataset directory exists
    if not os.path.exists(dataset_dir):
        print(f"Dataset directory '{dataset_dir}' not found!")
        print("Please create the dataset directory with person folders.")
        return None, None, None
    
    # Get all person categories
    categories = [d for d in os.listdir(dataset_dir) if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if len(categories) == 0:
        print("No person folders found in dataset directory!")
        return None, None, None
    
    print(f"Found {len(categories)} people: {categories}")
    
    total_images = 0
    processed_images = 0
    
    for category in categories:
        category_path = os.path.join(dataset_dir, category)
        print(f"\nProcessing {category}...")
        
        category_count = 0
        
        for img_file in os.listdir(category_path):
            if img_file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                total_images += 1
                img_path = os.path.join(category_path, img_file)
                
                try:
                    # Read image
                    img = cv2.imread(img_path)
                    if img is None:
                        continue
                    
                    # Convert to grayscale
                    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                    
                    # Detect faces
                    faces = face_cascade.detectMultiScale(gray, 1.1, 4, minSize=(30, 30))
                    
                    # Process each detected face
                    for (x, y, w, h) in faces:
                        # Extract face region
                        face = gray[y:y+h, x:x+w]
                        
                        # Resize to standard size
                        face_resized = cv2.resize(face, (img_size, img_size))
                        
                        # Normalize pixel values
                        face_normalized = face_resized / 255.0
                        
                        data.append(face_normalized)
                        labels.append(category)
                        
                        processed_images += 1
                        category_count += 1
                        
                except Exception as e:
                    print(f"Error processing {img_path}: {e}")
                    continue
        
        print(f"  Processed {category_count} face images for {category}")
    
    print(f"\nTotal images found: {total_images}")
    print(f"Total faces processed: {processed_images}")
    
    if len(data) == 0:
        print("No faces detected in any images!")
        return None, None, None
    
    # Convert to numpy arrays
    X = np.array(data).reshape(-1, img_size, img_size, 1)
    
    # Encode labels
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(labels)
    y_categorical = to_categorical(y_encoded)
    
    print(f"\nData shape: {X.shape}")
    print(f"Labels shape: {y_categorical.shape}")
    print(f"Number of classes: {len(label_encoder.classes_)}")
    
    return X, y_categorical, label_encoder

# Load and preprocess data
print("Loading and preprocessing data...")
X, y, label_encoder = load_and_preprocess_data(DATASET_DIR, IMG_SIZE)

if X is not None and y is not None:
    # Split data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"Training data shape: {X_train.shape}")
    print(f"Testing data shape: {X_test.shape}")
    print(f"Training labels shape: {y_train.shape}")
    print(f"Testing labels shape: {y_test.shape}")
else:
    print("Cannot proceed without data. Please check your dataset.")

def create_cnn_model(input_shape, num_classes):
    """
    Create an improved CNN model for face recognition
    """
    model = Sequential([
        # First Convolutional Block
        Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        # Second Convolutional Block
        Conv2D(64, (3, 3), activation='relu'),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        # Third Convolutional Block
        Conv2D(128, (3, 3), activation='relu'),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        # Fourth Convolutional Block
        Conv2D(256, (3, 3), activation='relu'),
        BatchNormalization(),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        # Flatten and Dense layers
        Flatten(),
        Dense(512, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        
        Dense(256, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        
        # Output layer
        Dense(num_classes, activation='softmax')
    ])
    
    return model

if X is not None and y is not None:
    # Create model
    num_classes = y.shape[1]
    input_shape = (IMG_SIZE, IMG_SIZE, 1)
    
    model = create_cnn_model(input_shape, num_classes)
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Display model summary
    model.summary()
else:
    print("Cannot create model without data.")

if X is not None and y is not None:
    # Define callbacks
    early_stopping = EarlyStopping(
        monitor='val_accuracy',
        patience=10,
        restore_best_weights=True,
        verbose=1
    )
    
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.2,
        patience=5,
        min_lr=0.0001,
        verbose=1
    )
    
    # Train the model
    print("Starting training...")
    history = model.fit(
        X_train, y_train,
        batch_size=BATCH_SIZE,
        epochs=EPOCHS,
        validation_data=(X_test, y_test),
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    print("Training completed!")
else:
    print("Cannot train model without data.")

if X is not None and y is not None:
    # Evaluate the model
    test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print(f"Test Loss: {test_loss:.4f}")
    
    # Plot training history
    plt.figure(figsize=(12, 4))
    
    # Plot accuracy
    plt.subplot(1, 2, 1)
    plt.plot(history.history['accuracy'], label='Training Accuracy')
    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
    plt.title('Model Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    
    # Plot loss
    plt.subplot(1, 2, 2)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    plt.tight_layout()
    plt.show()
else:
    print("Cannot evaluate model without data.")

if X is not None and y is not None:
    # Save the trained model
    model_path = os.path.join(MODEL_DIR, "face_recognition_model.h5")
    model.save(model_path)
    print(f"Model saved to: {model_path}")
    
    # Save the label encoder
    encoder_path = os.path.join(MODEL_DIR, "label_encoder.pkl")
    with open(encoder_path, "wb") as f:
        pickle.dump(label_encoder, f)
    print(f"Label encoder saved to: {encoder_path}")
    
    # Save class names for reference
    classes_path = os.path.join(MODEL_DIR, "classes.txt")
    with open(classes_path, "w") as f:
        for class_name in label_encoder.classes_:
            f.write(f"{class_name}\n")
    print(f"Class names saved to: {classes_path}")
    
    print("\nModel training and saving completed successfully!")
    print(f"Classes trained: {list(label_encoder.classes_)}")
else:
    print("Cannot save model without data.")